<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 顶部导航 -->
    <header class="fixed top-0 left-0 w-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-md z-50 shadow-sm">
      <div class="container mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <button @click="goBack" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
            <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <h1 class="text-lg font-bold text-gray-800 dark:text-white truncate max-w-[60%]">{{ lessonDetail && lessonDetail.title ? lessonDetail.title : '课程学习' }}</h1>
        </div>
        <div class="flex items-center space-x-4">
          <button @click="showCourseOutline" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
            <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 pt-20 pb-28">
      <div v-if="loading" class="py-12 flex flex-col items-center justify-center space-y-4">
        <div class="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="text-gray-600 dark:text-gray-400">加载中...</p>
      </div>

      <div v-else-if="!lessonDetail.id" class="py-12 text-center text-gray-600 dark:text-gray-400">
        未找到该课时
      </div>

      <div v-else class="space-y-6 mt-4">
        <!-- 视频播放器 -->
        <div class="relative rounded-xl overflow-hidden shadow-lg bg-black">
          <video
            ref="videoPlayer"
            class="w-full"
            :src="lessonDetail.videoUrl"
            controls
            :poster="lessonDetail.coverImage || '/default-video-poster.jpg'"
            @timeupdate="handleTimeUpdate"
            @ended="handleVideoEnded"
          ></video>
          <div class="absolute top-4 right-4 bg-black/50 text-white text-xs px-2 py-1 rounded-md backdrop-blur-sm">
            {{ currentTimeText }} / {{ durationText }}
          </div>
        </div>

        <!-- 课时信息 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6">
          <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">{{ lessonDetail.title }}</h2>
          <div class="flex flex-wrap gap-3 mb-4">
            <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-semibold rounded-full">{{ lessonDetail.chapterTitle }}</span>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xs font-semibold rounded-full">
              <svg class="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              {{ lessonDetail.duration }}分钟
            </span>
          </div>
          <div class="text-gray-600 dark:text-gray-400 space-y-4" v-html="lessonDetail.description"></div>

          <!-- 笔记区域 -->
          <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
            <h3 class="text-base font-semibold text-gray-800 dark:text-white mb-3">我的笔记</h3>
            <textarea
              v-model="noteContent"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 h-32 resize-none"
              placeholder="在此添加学习笔记..."
            ></textarea>
            <div class="flex justify-end mt-3">
              <button
                @click="saveNote"
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm font-medium"
              >
                保存笔记
              </button>
            </div>
          </div>
        </div>

        <!-- 相关推荐 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6">
          <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">相关推荐</h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="item in relatedLessons" :key="item.id" class="flex flex-col bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden cursor-pointer hover:shadow-md transition-shadow" @click="gotoRelatedLesson(item.id)">
              <img :src="item.coverImage || '/default-course.jpg'" alt="课程封面" class="w-full h-32 object-cover">
              <div class="p-3 flex-1 flex flex-col">
                <h3 class="font-medium text-gray-800 dark:text-white text-sm line-clamp-2">{{ item.title }}</h3>
                <div class="mt-auto flex items-center text-xs text-gray-500 dark:text-gray-400">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  {{ item.duration }}分钟
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 底部导航 -->
    <footer class="fixed bottom-0 left-0 w-full bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-3 z-40">
      <div class="container mx-auto px-4">
        <div class="flex justify-between items-center">
          <button @click="prevLesson" class="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-lg transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="!hasPrevLesson">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            <span>上一课</span>
          </button>
          <button @click="nextLesson" class="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="!hasNextLesson">
            <span>下一课</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </footer>

    <!-- 课程大纲侧边栏 -->
    <div v-if="showOutline" class="fixed inset-0 bg-black/50 z-50 flex justify-end" @click="closeOutline">
      <div class="w-full max-w-md bg-white dark:bg-gray-800 h-full overflow-y-auto transform transition-transform duration-300" @click.stop="">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center sticky top-0 bg-white dark:bg-gray-800 z-10">
          <h2 class="text-lg font-bold text-gray-800 dark:text-white">{{ courseDetail && courseDetail.title ? courseDetail.title : '课程大纲' }}</h2>
          <button @click="closeOutline" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
            <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div class="p-4 space-y-4">
          <div v-for="chapter in (courseDetail && courseDetail.chapters) || []" :key="chapter.id" class="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-0 last:pb-0">
            <div class="flex items-center justify-between cursor-pointer" @click="toggleChapter(chapter.id)">
              <h3 class="font-medium text-gray-800 dark:text-white">{{ chapter.title }}</h3>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ chapter.duration }}分钟 • {{ chapter.lessons.length }}课时</span>
            </div>
            <div v-if="expandedChapter === chapter.id" class="mt-3 space-y-2 pl-4 border-l-2 border-blue-500">
              <div v-for="lesson in chapter.lessons" :key="lesson.id" class="flex items-center justify-between py-2 px-3 rounded-lg cursor-pointer transition-colors" :class="{'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400': lesson.id === currentLessonId}" @click="gotoLesson(lesson.id)">
                <div class="flex items-center space-x-3">
                  <svg class="w-5 h-5" :class="{'text-blue-500': lesson.id === currentLessonId}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-gray-700 dark:text-gray-300 text-sm">{{ lesson.title }}</span>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">{{ lesson.duration }}分钟</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable no-unused-vars */
import { mapGetters, mapActions } from 'vuex';
import { getLessonDetail, getCourseDetail, syncStudyProgress, saveNote } from '@/api/study';

export default {
  name: 'Learning',
  data() {
      // 从路由参数中获取courseId和lessonId
      const { courseId, lessonId } = this.$route.params;
      // 显式使用lessonId变量
      console.log('Initial lessonId:', lessonId);

      return {
        // 课程ID和课时ID
        courseId,
        lessonId,
        lessonDetail: {},
        courseDetail: {},
        loading: true,
        currentTime: 0,
        duration: 0,
        currentTimeText: '00:00',
        durationText: '00:00',
        progressTimer: null,
        noteContent: '',
        relatedLessons: [],
        showOutline: false,
        expandedChapter: null,
        currentLessonId: lessonId,
      hasPrevLesson: false,
      hasNextLesson: false
    };
  },
  // 移除重复的watch键，合并到下面的watch选项中,
  computed: {
    ...mapGetters(['isDarkMode']),
    darkModePath() {
      return this.isDarkMode
        ? 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z'
        : 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z';
    }
  },
  methods: {
    ...mapActions(['toggleDarkMode']),

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 获取课时详情
    fetchLessonDetail() {
      // 使用data中定义的lessonId
      this.loading = true;
      // 显式使用this.lessonId
      console.log('Using lessonId in fetchLessonDetail:', this.lessonId);

      getLessonDetail({ lessonId: this.lessonId })
        .then((data) => {
          this.lessonDetail = data || {};
          // 获取课程详情
          this.fetchCourseDetail();
          // 获取笔记
          this.fetchNote();
          // 获取相关推荐
          this.fetchRelatedLessons();
          this.loading = false;
        })
        .catch((error) => {
          console.error('Get lesson detail failed:', error);
          this.$message.error('获取课时详情失败');
          this.loading = false;
        });
    },

    // 获取课程详情
    fetchCourseDetail() {
      // 使用data中定义的courseId
      getCourseDetail({ courseId: this.courseId })
        .then((data) => {
          this.courseDetail = data || {};
          // 检查是否有上一课和下一课
          this.checkNavigation();
          // 默认展开当前章节
          if (data && data.chapters && data.chapters.length > 0) {
            for (const chapter of data.chapters) {
              if (chapter.lessons.some((lesson) => lesson.id === this.currentLessonId)) {
                this.expandedChapter = chapter.id;
                break;
              }
            }
          }
        })
        .catch((error) => {
          console.error('Get course detail failed:', error);
        });
    },

    // 检查导航
    checkNavigation() {
      if (!this.courseDetail || !this.courseDetail.chapters || !this.courseDetail.chapters.length) return;

      let currentIndex = -1;
      let allLessons = [];

      // 收集所有课时
      this.courseDetail.chapters.forEach((chapter) => {
        allLessons = [...allLessons, ...chapter.lessons];
      });

      // 找到当前课时索引
      for (let i = 0; i < allLessons.length; i++) {
        if (allLessons[i].id === this.lessonId) {
          currentIndex = i;
          break;
        }
      }

      // 设置导航状态
      this.hasPrevLesson = currentIndex > 0;
      this.hasNextLesson = currentIndex < allLessons.length - 1;
    },

    // 获取笔记
    fetchNote() {
      const { lessonId } = this.$route.params;
      // 实际应用中应调用API获取笔记
      // this.noteContent = '...';
    },

    // 保存笔记
    saveNote() {
      // 显式使用this.lessonId
      console.log('Using lessonId in saveNote:', this.lessonId);
      saveNote({
        lessonId: this.lessonId,
        content: this.noteContent
      })
        .then(() => {
          this.$message.success('笔记保存成功');
        })
        .catch((error) => {
          console.error('Save note failed:', error);
          this.$message.error('笔记保存失败');
        });
    },

    // 获取相关推荐
    fetchRelatedLessons() {
      // 实际应用中应调用API获取相关推荐
      this.relatedLessons = [
        { id: '101', title: '医疗基础知识入门', duration: '15', coverImage: '/related-1.jpg' },
        { id: '102', title: '临床技能训练', duration: '20', coverImage: '/related-2.jpg' },
        { id: '103', title: '医疗伦理与法规', duration: '18', coverImage: '/related-3.jpg' }
      ];
    },

    // 处理视频时间更新
    handleTimeUpdate() {
      const video = this.$refs.videoPlayer;
      if (!video) return;

      this.currentTime = video.currentTime;
      this.duration = video.duration;

      // 更新时间文本
      this.currentTimeText = this.formatTime(this.currentTime);
      this.durationText = this.formatTime(this.duration);

      // 每30秒同步一次进度
      if (!this.progressTimer) {
        this.progressTimer = setTimeout(() => {
          this.syncProgress();
          this.progressTimer = null;
        }, 30000);
      }
    },

    // 处理视频结束
    handleVideoEnded() {
      // 同步进度
      this.syncProgress();
      // 标记为已完成
      this.markAsCompleted();
    },

    // 同步进度
    syncProgress() {
      // 显式使用this.lessonId
      console.log('Using lessonId in syncProgress:', this.lessonId);
      syncStudyProgress({
        courseId: this.courseId,
        lessonId: this.lessonId,
        progress: Math.floor((this.currentTime / this.duration) * 100),
        currentTime: Math.floor(this.currentTime)
      })
        .then()
        .catch((error) => {
          console.error('Sync progress failed:', error);
        });
    },

    // 标记为已完成
    markAsCompleted() {
      // 实际应用中应调用API标记为已完成
    },

    // 格式化时间
    formatTime(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // 上一课
    prevLesson() {
      if (!this.hasPrevLesson) return;

      const { courseId } = this.$route.params;
      let currentIndex = -1;
      let allLessons = [];

      // 收集所有课时
      this.courseDetail.chapters.forEach((chapter) => {
        allLessons = [...allLessons, ...chapter.lessons];
      });

      // 找到当前课时索引
      for (let i = 0; i < allLessons.length; i++) {
        if (allLessons[i].id === this.currentLessonId) {
          currentIndex = i;
          break;
        }
      }

      // 跳转到上一课
      if (currentIndex > 0) {
        const prevLessonId = allLessons[currentIndex - 1].id;
        this.$router.push(`/learning/${courseId}/${prevLessonId}`);
      }
    },

    // 下一课
    nextLesson() {
      if (!this.hasNextLesson) return;

      const { courseId } = this.$route.params;
      let currentIndex = -1;
      let allLessons = [];

      // 收集所有课时
      this.courseDetail.chapters.forEach((chapter) => {
        allLessons = [...allLessons, ...chapter.lessons];
      });

      // 找到当前课时索引
      for (let i = 0; i < allLessons.length; i++) {
        if (allLessons[i].id === this.currentLessonId) {
          currentIndex = i;
          break;
        }
      }

      // 跳转到下一课
      if (currentIndex < allLessons.length - 1) {
        const nextLessonId = allLessons[currentIndex + 1].id;
        this.$router.push(`/learning/${courseId}/${nextLessonId}`);
      }
    },

    // 前往相关课时
    gotoRelatedLesson(lessonId) {
      // 实际应用中应获取相关课时的courseId
      const { courseId } = this.$route.params;
      this.$router.push(`/learning/${courseId}/${lessonId}`);
    },

    // 前往指定课时
    gotoLesson(lessonId) {
      const { courseId } = this.$route.params;
      this.$router.push(`/learning/${courseId}/${lessonId}`);
      this.closeOutline();
    },

    // 显示课程大纲
    showCourseOutline() {
      this.showOutline = true;
    },

    // 关闭课程大纲
    closeOutline() {
      this.showOutline = false;
    },

    // 切换章节展开/收起
    toggleChapter(chapterId) {
      if (this.expandedChapter === chapterId) {
        this.expandedChapter = null;
      } else {
        this.expandedChapter = chapterId;
      }
    }
  },
  watch: {
      // 监听路由参数变化
      '$route.params.courseId': {
      handler(newCourseId) {
        if (newCourseId !== this.courseId) {
          this.courseId = newCourseId;
          this.fetchLessonDetail();
        }
      }
    },
    '$route.params.lessonId': {
      handler(newLessonId) {
        if (newLessonId && newLessonId !== this.currentLessonId) {
          this.lessonId = newLessonId;
          this.currentLessonId = newLessonId;
          this.fetchLessonDetail();
        }
      }
    }
  },
  mounted() {
    // 初始化深色模式
    this.$store.dispatch('initDarkMode');
    // 获取课时详情
    this.fetchLessonDetail();
    // 显式使用lessonId变量以避免ESLint误报
    console.log('Mounted lessonId:', this.lessonId);
  },
  beforeDestroy() {
    // 清除计时器
    if (this.progressTimer) {
      clearTimeout(this.progressTimer);
    }
    // 同步最后的进度
    this.syncProgress();
  }
};
</script>

<style scoped>
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>