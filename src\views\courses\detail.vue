<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 顶部导航 -->
    <header class="fixed top-0 left-0 w-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-md z-50 shadow-sm">
      <div class="container mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <button @click="goBack" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
            <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <span class="text-lg font-bold text-gray-800 dark:text-white">课程详情</span>
        </div>
        <div class="flex items-center space-x-4">
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 pt-20 pb-20">
      <div v-if="loading" class="py-12 flex flex-col items-center justify-center space-y-4">
        <div class="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="text-gray-600 dark:text-gray-400">加载中...</p>
      </div>

      <div v-else-if="!courseDetail.id" class="py-12 text-center text-gray-600 dark:text-gray-400">
        未找到该课程
      </div>

      <div v-else class="space-y-6 mt-6">
        <!-- 课程封面 -->
        <div class="relative rounded-xl overflow-hidden shadow-lg">
          <img :src="courseDetail.coverImage || '/default-course.jpg'" alt="课程封面" class="w-full h-48 object-cover md:h-64">
          <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6">
            <span class="inline-block px-3 py-1 bg-blue-600 text-white text-xs font-semibold rounded-full mb-3 w-fit">{{ courseDetail.category }}</span>
            <h1 class="text-2xl md:text-3xl font-bold text-white mb-2">{{ courseDetail.title }}</h1>
            <div class="flex items-center text-white/90 text-sm space-x-4">
              <span class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {{ courseDetail.duration }}分钟
              </span>
              <span class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
                {{ courseDetail.studentCount }}人学习
              </span>
              <span class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
                {{ courseDetail.rating }}分
              </span>
            </div>
          </div>
        </div>

        <!-- 课程信息 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="md:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow p-6">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">课程简介</h2>
            <div class="text-gray-600 dark:text-gray-400 space-y-4" v-html="courseDetail.description"></div>

            <h2 class="text-lg font-bold text-gray-800 dark:text-white mt-8 mb-4">课程大纲</h2>
            <div class="space-y-4">
              <div v-for="chapter in courseDetail.chapters" :key="chapter.id" class="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-0 last:pb-0">
                <div class="flex items-center justify-between cursor-pointer" @click="toggleChapter(chapter.id)">
                  <h3 class="font-medium text-gray-800 dark:text-white">{{ chapter.title }}</h3>
                  <span class="text-sm text-gray-500 dark:text-gray-400">{{ chapter.duration }}分钟 • {{ chapter.lessons.length }}课时</span>
                </div>
                <div v-if="expandedChapter === chapter.id" class="mt-3 space-y-2 pl-4 border-l-2 border-blue-500">
                  <div v-for="lesson in chapter.lessons" :key="lesson.id" class="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors" @click="gotoLearning(lesson.id)">
                    <div class="flex items-center space-x-3">
                      <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span class="text-gray-700 dark:text-gray-300 text-sm">{{ lesson.title }}</span>
                    </div>
                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ lesson.duration }}分钟</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="md:col-span-1 space-y-6">
            <!-- 讲师信息 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6">
              <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">讲师信息</h2>
              <div class="flex items-center space-x-4">
                <img :src="courseDetail.teacher.avatar || '/default-avatar.png'" alt="讲师头像" class="w-14 h-14 rounded-full object-cover border-2 border-white dark:border-gray-700 shadow-md">
                <div>
                  <h3 class="font-medium text-gray-800 dark:text-white">{{ courseDetail.teacher.name }}</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">{{ courseDetail.teacher.title }}</p>
                </div>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-3 line-clamp-3">{{ courseDetail.teacher.bio }}</p>
            </div>

            <!-- 课程操作 -->
            <div class="sticky top-24 bg-white dark:bg-gray-800 rounded-xl shadow p-6">
              <div v-if="isEnrolled" class="flex flex-col space-y-3">
                <button @click="gotoLearningFirstLesson()" class="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium shadow-md hover:shadow-lg flex items-center justify-center">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  继续学习
                </button>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">学习进度</span>
                    <span class="text-sm font-medium text-blue-600 dark:text-blue-400">{{ progress }}%</span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full" :style="{ width: progress + '%' }"></div>
                  </div>
                </div>
              </div>
              <div v-else>
                <button @click="enrollCourse()" class="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium shadow-md hover:shadow-lg mb-3">
                  立即报名
                </button>
                <div class="text-center text-sm text-gray-500 dark:text-gray-400">
                  免费课程 • 限时报名
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 底部导航 -->
    <footer class="fixed bottom-0 left-0 w-full bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-2 z-40">
      <div class="container mx-auto">
        <div class="flex justify-around">
          <a href="/home" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
            </svg>
            <span class="text-xs mt-1">首页</span>
          </a>
          <a href="/courses" class="flex flex-col items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
              <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
            </svg>
            <span class="text-xs mt-1">课程</span>
          </a>
          <a href="/study-center" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-xs mt-1">学习</span>
          </a>
          <a href="/profile" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-xs mt-1">我的</span>
          </a>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import { getCourseDetail, enrollCourse, getCourseProgress } from '@/api/course';

export default {
  name: 'CourseDetail',
  data() {
    return {
      courseDetail: {},
      loading: true,
      expandedChapter: null,
      progress: 0,
      isEnrolled: false
    };
  },
  methods: {
    ...mapActions(['toggleDarkMode']),

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 获取课程详情
    fetchCourseDetail() {
      this.loading = true;

      // 使用课程ID获取课程详情
      getCourseDetail({ courseId: this.$route.params.id })
        .then((data) => {
          this.courseDetail = data || {};
          // 默认展开第一章
          if (data && data.chapters && data.chapters.length > 0) {
            this.expandedChapter = data.chapters[0].id;
          }
          this.loading = false;
          // 检查是否已报名
          this.checkEnrollment();
        })
        .catch((error) => {
          console.error('Get course detail failed:', error);
          this.$message.error('获取课程详情失败');
          this.loading = false;
        });
    },

    // 检查是否已报名
    checkEnrollment() {
      // 这里应该调用API检查是否已报名
      // 为简化示例，这里假设已报名
      this.isEnrolled = true; // 实际应用中应根据API返回结果设置
      if (this.isEnrolled) {
        this.fetchProgress();
      }
    },

    // 获取学习进度
    fetchProgress() {
      getCourseProgress({ courseId: this.$route.params.id })
        .then((data) => {
          this.progress = data.progress || 0;
        })
        .catch((error) => {
          console.error('Get course progress failed:', error);
        });
    },

    // 切换章节展开/收起
    toggleChapter(chapterId) {
      if (this.expandedChapter === chapterId) {
        this.expandedChapter = null;
      } else {
        this.expandedChapter = chapterId;
      }
    },

    // 前往学习
    gotoLearning(lessonId) {
      const { id } = this.$route.params;
      this.$router.push(`/learning/${id}/${lessonId}`);
    },

    // 前往第一个课时学习
    gotoLearningFirstLesson() {
      if (
        this.courseDetail && this.courseDetail.chapters && this.courseDetail.chapters.length > 0 &&
        this.courseDetail.chapters[0].lessons && this.courseDetail.chapters[0].lessons.length > 0
      ) {
        const firstLessonId = this.courseDetail.chapters[0].lessons[0].id;
        this.gotoLearning(firstLessonId);
      }
    },

    // 报名课程
    enrollCourse() {
      const { id } = this.$route.params;
      enrollCourse({ courseId: id })
        .then(() => {
          this.isEnrolled = true;
          this.fetchProgress();
          this.$message.success('报名成功');
        })
        .catch((error) => {
          console.error('Enroll course failed:', error);
          this.$message.error('报名失败，请重试');
        });
    }
  },
  computed: {
    ...mapGetters(['isDarkMode']),
    darkModePath() {
      return this.isDarkMode
        ? 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z'
        : 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z';
    }
  },
  watch: {
    '$route.params.id': {
      handler(newId) {
        if (newId) {
          this.fetchCourseDetail();
        }
      }
    }
  },
  mounted() {
    // 初始化深色模式
    this.$store.dispatch('initDarkMode');
    // 获取课程详情
    this.fetchCourseDetail();
  }
};
</script>

<style scoped>
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>