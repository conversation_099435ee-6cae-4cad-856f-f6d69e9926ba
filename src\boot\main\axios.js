import axios from 'axios';

// 创建axios实例
const axiosInstance = axios.create({
  baseURL: process.env.NODE_ENV === 'development' ? '/api' : process.env.VUE_APP_API_URL,
  timeout: 50000, // 请求超时时间
  withCredentials: true // 允许携带cookie
});

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    // 从localStorage中获取token
    const token = localStorage.getItem('token');
    // 如果token存在，则添加到请求头中
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加AppID
    config.headers['X-App-ID'] = process.env.VUE_APP_ID || 'medical-training-system';

    return config;
  },
  (error) => {
    // 处理请求错误
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    // 处理响应数据
    const res = response.data;

    // 检查是否成功
    if (res.Code !== 200) {
      // 错误处理
      console.error('Response error:', res.Message || 'Request failed');
      return Promise.reject(new Error(res.Message || 'Request failed'));
    }

    return res.Data;
  },
  (error) => {
    // 处理响应错误
    console.error('Response error:', error.message);

    // 处理常见错误
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 未授权，跳转到登录页
          window.location.href = '/login';
          break;
        case 403:
          // 禁止访问
          console.error('Access denied');
          break;
        case 500:
          // 服务器错误
          console.error('Server error');
          break;
        default:
          console.error(`Error: ${error.response.status}`);
      }
    }

    return Promise.reject(error);
  }
);

export { axiosInstance };