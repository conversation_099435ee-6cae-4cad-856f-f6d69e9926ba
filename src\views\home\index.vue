<template>
    <div
        class="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300"
    >
        <!-- 顶部导航 -->
        <header
            class="fixed top-0 left-0 w-full bg-white dark:bg-gray-800 z-50 shadow-sm"
        >
            <div
                class="container mx-auto px-4 py-3 flex justify-between items-center"
            >
                <div class="flex items-center space-x-2">
                    <img src="@/assets/new-logo.svg" class="w-6 h-6 text-blue-600 dark:text-blue-400" alt="系统logo">
                    <span
                        class="text-lg font-bold text-gray-800 dark:text-white"
                        >医疗培训系统</span
                    >
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative" v-if="currentUser">
                        <img
                            :src="currentUser.avatar || '/default-avatar.png'"
                            alt="用户头像"
                            class="w-8 h-8 rounded-full border-2 border-blue-500 cursor-pointer"
                            @click="toggleUserMenu"
                        />
                        <div
                            v-if="showUserMenu"
                            class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-2 z-10"
                        >
                            <a
                                href="/profile"
                                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                >个人资料</a
                            >
                            <a
                                href="#"
                                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                >设置</a
                            >
                            <div
                                class="border-t border-gray-200 dark:border-gray-700 my-1"
                            ></div>
                            <a
                                href="#"
                                class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                                @click="handleLogout"
                                >退出登录</a
                            >
                        </div>
                        <SwiperSlide
                            class="bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center text-white p-4"
                        >
                        </SwiperSlide>
                        <SwiperSlide
                            class="bg-gradient-to-r from-green-500 to-teal-600 flex items-center justify-center text-white p-4"
                        >
                        </SwiperSlide>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="container mx-auto px-4 pt-16 pb-20">
            <!-- 快捷功能区 -->
            <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-8">
                <div
                    class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-sm p-5 flex flex-col items-center justify-center hover:shadow-md hover:-translate-y-1 transition-all duration-300"
                >
                    <div
                        class="w-14 h-14 rounded-full bg-blue-500/10 dark:bg-blue-500/20 flex items-center justify-center mb-3"
                    >
                        <img src="@/assets/courses-icon.svg" class="w-7 h-7 text-blue-600 dark:text-blue-400" alt="我的课程">
                    </div>
                    <span class="text-base font-medium text-gray-800 dark:text-gray-200">我的课程</span>
                </div>

                <div
                    class="bg-gradient-to-br from-green-50 to-green-100 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-sm p-5 flex flex-col items-center justify-center hover:shadow-md hover:-translate-y-1 transition-all duration-300"
                >
                    <div
                        class="w-14 h-14 rounded-full bg-green-500/10 dark:bg-green-500/20 flex items-center justify-center mb-3"
                    >
                        <img src="@/assets/study-icon.svg" class="w-7 h-7 text-green-600 dark:text-green-400" alt="学习记录">
                    </div>
                    <span class="text-base font-medium text-gray-800 dark:text-gray-200">学习记录</span>
                </div>

                <div
                    class="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-sm p-5 flex flex-col items-center justify-center hover:shadow-md hover:-translate-y-1 transition-all duration-300"
                >
                    <div
                        class="w-14 h-14 rounded-full bg-yellow-500/10 dark:bg-yellow-500/20 flex items-center justify-center mb-3"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
</svg>
                    </div>
                    <span class="text-base font-medium text-gray-800 dark:text-gray-200">考试中心</span>
                </div>

                <div
                    class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-sm p-5 flex flex-col items-center justify-center hover:shadow-md hover:-translate-y-1 transition-all duration-300"
                >
                    <div
                        class="w-14 h-14 rounded-full bg-purple-500/10 dark:bg-purple-500/20 flex items-center justify-center mb-3"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
</svg>
                    </div>
                    <span class="text-base font-medium text-gray-800 dark:text-gray-200">证书查询</span>
                </div>
            </div>

            <!-- 轮播图 -->
            <div
                class="relative bg-white dark:bg-gray-800 rounded-2xl shadow overflow-hidden mb-8"
            >
                <Swiper class="aspect-video w-full relative overflow-hidden" :options="swiperOptions">
                    <SwiperSlide class="relative">
                        <img src="/default-course.jpg" alt="轮播图1" class="w-full h-full object-cover"/>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                        <div class="text-center text-white">
                            <h2 class="text-xl md:text-2xl font-bold mb-2">
                                2025年最新医疗培训课程
                            </h2>
                            <p class="mb-4">提升专业技能，掌握最新医疗知识</p>
                            <button
                                class="bg-white text-blue-600 px-4 py-2 rounded-full font-medium hover:bg-opacity-90 transition-colors"
                            >
                                立即查看
                            </button>
                        </div>
                        </div>
                    </SwiperSlide>
                    <SwiperSlide class="relative">
                        <img src="/course-1.jpg" alt="轮播图2" class="w-full h-full object-cover"/>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                        <div class="text-center">
                            <h2 class="text-xl md:text-2xl font-bold mb-2">
                                医疗行业最新政策解读
                            </h2>
                            <p class="mb-4">了解行业动态，把握发展方向</p>
                            <button
                                class="bg-white text-green-600 px-4 py-2 rounded-full font-medium hover:bg-opacity-90 transition-colors"
                            >
                                立即学习
                            </button>
                        </div>
                    </div>
                    </SwiperSlide>
                    <SwiperSlide class="relative">
                        <img src="/course-2.jpg" alt="轮播图3" class="w-full h-full object-cover"/>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                        <div class="text-center">
                            <h2 class="text-xl md:text-2xl font-bold mb-2">
                                急救技能培训课程
                            </h2>
                            <p class="mb-4">掌握急救知识，挽救生命关键时刻</p>
                            <button
                                class="bg-white text-orange-600 px-4 py-2 rounded-full font-medium hover:bg-opacity-90 transition-colors"
                            >
                                立即报名
                            </button>
                        </div>
                    </div>
                    </SwiperSlide>
                    <!-- 轮播指示器 -->
                    <div class="swiper-pagination"></div>
                    <!-- 轮播导航按钮 -->
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-button-next"></div>
                </Swiper>
            </div>

            <!-- 推荐课程 -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-800 dark:text-white">
                        推荐课程
                    </h2>
                    <a
                        href="/courses"
                        class="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                        >查看全部</a
                    >
                </div>

                <div
                    class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
                >
                    <!-- 课程卡片 1 -->
                    <div
                        class="bg-white dark:bg-gray-800 rounded-xl shadow overflow-hidden hover:shadow-lg transition-shadow group"
                    >
                        <div class="relative h-40">
                            <img
                                src="/course-1.jpg"
                                alt="课程封面"
                                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                            />
                            <div
                                class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full"
                            >
                                热门
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span
                                    class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2"
                                    >内科</span
                                >
                                <span
                                    class="text-xs text-gray-500 dark:text-gray-400"
                                    >32课时</span
                                >
                            </div>
                            <h3
                                class="font-bold text-lg mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"
                            >
                                内科学基础知识与临床应用
                            </h3>
                            <p
                                class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2"
                            >
                                本课程涵盖基础理论知识和临床应用技能，适合医疗工作者提升专业水平。
                            </p>
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <img
                                        src="/teacher-1.jpg"
                                        alt="讲师头像"
                                        class="w-8 h-8 rounded-full mr-2"
                                    />
                                    <span
                                        class="text-sm text-gray-700 dark:text-gray-300"
                                        >张教授</span
                                    >
                                </div>
                                <div
                                    class="text-blue-600 dark:text-blue-400 font-bold"
                                >
                                    ¥199
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 课程卡片 2 -->
                    <div
                        class="bg-white dark:bg-gray-800 rounded-xl shadow overflow-hidden hover:shadow-lg transition-shadow group"
                    >
                        <div class="relative h-40">
                            <img
                                src="/course-2.jpg"
                                alt="课程封面"
                                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                            />
                            <div
                                class="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full"
                            >
                                新课
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span
                                    class="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded mr-2"
                                    >外科</span
                                >
                                <span
                                    class="text-xs text-gray-500 dark:text-gray-400"
                                    >24课时</span
                                >
                            </div>
                            <h3
                                class="font-bold text-lg mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"
                            >
                                外科手术技巧与案例分析
                            </h3>
                            <p
                                class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2"
                            >
                                通过实际案例分析，掌握外科手术的关键技巧和注意事项，提高手术成功率。
                            </p>
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <img
                                        src="/teacher-2.jpg"
                                        alt="讲师头像"
                                        class="w-8 h-8 rounded-full mr-2"
                                    />
                                    <span
                                        class="text-sm text-gray-700 dark:text-gray-300"
                                        >李医生</span
                                    >
                                </div>
                                <div
                                    class="text-blue-600 dark:text-blue-400 font-bold"
                                >
                                    ¥259
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 课程卡片 3 -->
                    <div
                        class="bg-white dark:bg-gray-800 rounded-xl shadow overflow-hidden hover:shadow-lg transition-shadow group"
                    >
                        <div class="relative h-40">
                            <img
                                    src="/default-course.jpg"
                                    alt="课程封面"
                                    class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                                />
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span
                                    class="text-xs bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded mr-2"
                                    >护理</span
                                >
                                <span
                                    class="text-xs text-gray-500 dark:text-gray-400"
                                    >18课时</span
                                >
                            </div>
                            <h3
                                class="font-bold text-lg mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"
                            >
                                高级护理技巧与沟通艺术
                            </h3>
                            <p
                                class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2"
                            >
                                提升护理专业技能，学习与患者有效沟通的技巧，提高护理质量和患者满意度。
                            </p>
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <img
                                        src="/default-teacher.jpg"
                                        alt="讲师头像"
                                        class="w-8 h-8 rounded-full mr-2"
                                    />
                                    <span
                                        class="text-sm text-gray-700 dark:text-gray-300"
                                        >王护士</span
                                    >
                                </div>
                                <div
                                    class="text-blue-600 dark:text-blue-400 font-bold"
                                >
                                    ¥159
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最新资讯 -->
            <div>
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-800 dark:text-white">
                        最新资讯
                    </h2>
                    <a
                        href="#"
                        class="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                        >查看全部</a
                    >
                </div>

                <div class="space-y-4">
                    <div
                        class="bg-white dark:bg-gray-800 rounded-xl shadow p-4 hover:shadow-md transition-shadow"
                    >
                        <div class="flex flex-col md:flex-row gap-4">
                            <img
                                    src="/course-1.jpg"
                                    alt="资讯图片"
                                    class="w-full md:w-1/3 h-40 object-cover rounded-lg"
                                />
                            <div class="flex-1">
                                <h3
                                    class="font-bold text-lg mb-2 hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer"
                                >
                                    国家卫健委发布最新医疗行业标准
                                </h3>
                                <p
                                    class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2"
                                >
                                    国家卫生健康委员会近日发布了最新的医疗行业标准，涵盖了医疗机构管理、医疗服务质量控制等多个方面。
                                </p>
                                <div
                                    class="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400"
                                >
                                    <span>2025-08-01</span>
                                    <span>政策解读</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div
                        class="bg-white dark:bg-gray-800 rounded-xl shadow p-4 hover:shadow-md transition-shadow"
                    >
                        <div class="flex flex-col md:flex-row gap-4">
                            <img
                                    src="/course-2.jpg"
                                    alt="资讯图片"
                                    class="w-full md:w-1/3 h-40 object-cover rounded-lg"
                                />
                            <div class="flex-1">
                                <h3
                                    class="font-bold text-lg mb-2 hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer"
                                >
                                    医疗人工智能应用新进展
                                </h3>
                                <p
                                    class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2"
                                >
                                    人工智能技术在医疗领域的应用取得新进展，智能诊断系统准确率达到95%以上，为临床决策提供重要支持。
                                </p>
                                <div
                                    class="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400"
                                >
                                    <span>2025-07-28</span>
                                    <span>技术前沿</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部导航 -->
        <footer
            class="fixed bottom-0 left-0 w-full bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-2 z-40"
        >
            <div class="container mx-auto">
                <div class="flex justify-around">
                    <a
                        href="/home"
                        class="flex flex-col items-center text-blue-600 dark:text-blue-400"
                    >
                        <svg
                            class="w-6 h-6"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
                            ></path>
                        </svg>
                        <span class="text-xs mt-1">首页</span>
                    </a>
                    <a
                        href="/courses"
                        class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                        <svg
                            class="w-6 h-6"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"
                            ></path>
                            <path
                                d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"
                            ></path>
                        </svg>
                        <span class="text-xs mt-1">课程</span>
                    </a>
                    <a
                        href="/study-center"
                        class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                        <svg
                            class="w-6 h-6"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <span class="text-xs mt-1">学习</span>
                    </a>
                    <a
                        href="/profile"
                        class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                        <svg
                            class="w-6 h-6"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <span class="text-xs mt-1">我的</span>
                    </a>
                </div>
            </div>
        </footer>
    </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import { logout } from "@/api/user";
import { getCourseList } from "@/api/course";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import "swiper/swiper-bundle.css";

export default {
    name: "Home",
    components: {
        Swiper,
        SwiperSlide,
    },
    data() {
        return {
            showUserMenu: false,
            courseList: [],
            swiperOptions: {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: true,
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                },
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                },
            },
        };
    },
    computed: {
        ...mapGetters(["isDarkMode", "user/currentUser"]),
        darkModePath() {
            return this.isDarkMode
                ? "M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                : "M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z";
        },
    },
    methods: {
        ...mapActions(["toggleDarkMode"]),

        // 切换用户菜单
        toggleUserMenu() {
            this.showUserMenu = !this.showUserMenu;
        },

        // 退出登录
        handleLogout() {
            logout()
                .then(() => {
                    this.$store.dispatch("logout");
                    this.$message.success("退出登录成功");
                    this.$router.push("/login");
                })
                .catch((error) => {
                    this.$message.error("退出登录失败: " + error.message);
                });
            this.showUserMenu = false;
        },

        gotoCourseDetail(id) {
            this.$router.push({ path: `/courses/detail/${id}` });
        },
        gotoCategory(id) {
            this.$router.push({ path: `/courses/category/${id}` });
        },
        gotoProfile() {
            this.$router.push({ path: "/profile" });
            this.showUserMenu = false;
        },
        gotoSettings() {
            this.$router.push({ path: "/settings" });
            this.showUserMenu = false;
        },

        // 获取推荐课程
        getRecommendedCourses() {
            getCourseList({
                page: 1,
                pageSize: 3,
                recommended: true,
            })
                .then((data) => {
                    this.courseList = data.list || [];
                })
                .catch((error) => {
                    console.error("Get recommended courses failed:", error);
                    this.$message.error("获取推荐课程失败");
                });
        },
    },
    mounted() {
        // 初始化深色模式
        this.$store.dispatch("initDarkMode");
        // 获取推荐课程
        this.getRecommendedCourses();

        // 点击页面其他地方关闭用户菜单
        document.addEventListener("click", (e) => {
            if (
                !this.$el.contains(e.target) &&
                !e.target.closest(".relative")
            ) {
                this.showUserMenu = false;
            }
        });
    },
};
</script>

<style scoped>
/* 自定义样式 */
.swiper-container {
    width: 100%;
    height: 100%;
    min-height: 200px;
    padding: 0;
    margin: 0;
}

.swiper-slide {
    height: 100%;
}

.swiper-slide img {
    height: 100%;
    width: 100%;
    object-fit: cover;
}

.swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
}

.swiper-pagination-bullet {
    background-color: rgba(255, 255, 255, 0.7);
}

.swiper-pagination-bullet-active {
    background-color: white;
}

.swiper-button-prev,
.swiper-button-next {
    color: white;
}

/* 动画效果 */
.swiper-slide {
    transition: transform 0.5s ease;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
