<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 顶部导航 -->
    <header class="fixed top-0 left-0 w-full bg-white dark:bg-gray-800 z-50 shadow-sm">
      <div class="container mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <img src="@/assets/new-logo.svg" alt="系统logo" class="w-6 h-6 text-blue-600 dark:text-blue-400">
          <span class="text-lg font-bold text-gray-800 dark:text-white">医疗培训系统</span>
        </div>
        <div class="flex items-center space-x-4">
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 pt-16 pb-20">
      <!-- 用户信息卡片 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6 mb-8">
        <div class="flex flex-col md:flex-row items-center md:items-start gap-6">
          <div class="relative">
            <img
              :src="userInfo.avatar || '/default-avatar.png'"
              alt="用户头像"
              class="w-24 h-24 rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-md"
            >
            <button class="absolute bottom-0 right-0 w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center hover:bg-blue-600 transition-colors cursor-pointer shadow-md">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
              </svg>
            </button>
          </div>
          <div class="flex-1">
            <div class="flex flex-col md:flex-row md:items-center justify-between mb-4">
              <div>
                <h1 class="text-xl font-bold text-gray-800 dark:text-white">{{ userInfo.name || '未设置' }}</h1>
                <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">{{ userInfo.title || '未设置职位' }}</p>
              </div>
              <button class="mt-3 md:mt-0 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm font-medium">
                编辑资料
              </button>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 text-sm">
              <div class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                {{ userInfo.email || '未设置邮箱' }}
              </div>
              <div class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                {{ userInfo.phone || '未设置电话' }}
              </div>
              <div class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                {{ userInfo.department || '未设置部门' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 学习数据统计 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6 hover:shadow-md transition-shadow">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-gray-500 dark:text-gray-400 text-sm font-medium">已完成课程</h3>
            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>
          <div class="flex items-end justify-between">
            <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ stats.completedCourses || 0 }}</p>
            <p class="text-sm text-green-500 dark:text-green-400 flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
              </svg>
              {{ stats.completedCoursesPercent || 0 }}%
            </p>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6 hover:shadow-md transition-shadow">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-gray-500 dark:text-gray-400 text-sm font-medium">学习时长</h3>
            <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="flex items-end justify-between">
            <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ stats.studyHours || 0 }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">小时</p>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6 hover:shadow-md transition-shadow">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-gray-500 dark:text-gray-400 text-sm font-medium">获得证书</h3>
            <div class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
              </svg>
            </div>
          </div>
          <div class="flex items-end justify-between">
            <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ stats.certificates || 0 }}</p>
            <p class="text-sm text-green-500 dark:text-green-400 flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
              </svg>
              {{ stats.certificatesPercent || 0 }}%
            </p>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6 hover:shadow-md transition-shadow">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-gray-500 dark:text-gray-400 text-sm font-medium">学习积分</h3>
            <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
          </div>
          <div class="flex items-end justify-between">
            <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ stats.points || 0 }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">积分</p>
          </div>
        </div>
      </div>

      <!-- 选项卡 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow mb-8">
        <div class="border-b border-gray-200 dark:border-gray-700">
          <div class="flex overflow-x-auto hide-scrollbar">
            <button
              class="px-6 py-4 text-sm font-medium border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 whitespace-nowrap"
              :class="activeTab === 'learning' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'"
              @click="activeTab = 'learning'"
            >
              学习记录
            </button>
            <button
              class="px-6 py-4 text-sm font-medium border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap"
              :class="activeTab === 'certificates' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'"
              @click="activeTab = 'certificates'"
            >
              我的证书
            </button>
            <button
              class="px-6 py-4 text-sm font-medium border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap"
              :class="activeTab === 'settings' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'"
              @click="activeTab = 'settings'"
            >
              账户设置
            </button>
          </div>
        </div>

        <!-- 学习记录 -->
        <div v-if="activeTab === 'learning'" class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white">最近学习记录</h2>
            <a href="/learning-history" class="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium">查看全部</a>
          </div>

          <div v-if="loading" class="space-y-4">
            <div class="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
            <div class="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
            <div class="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          </div>

          <div v-else-if="learningRecords.length === 0" class="text-center py-12 text-gray-500 dark:text-gray-400">
            暂无学习记录
          </div>

          <div v-else class="space-y-4">
            <div v-for="record in learningRecords" :key="record.id" class="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer" @click="gotoCourse(record.courseId)">
              <img :src="record.courseCover || '/default-course.jpg'" alt="课程封面" class="w-16 h-16 object-cover rounded-lg mr-4">
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-gray-800 dark:text-white truncate">{{ record.courseTitle }}</h4>
                <p class="text-gray-500 dark:text-gray-400 text-sm truncate">{{ record.lessonTitle }}</p>
              </div>
              <div class="ml-4 flex flex-col items-end">
                <span class="text-xs text-gray-500 dark:text-gray-400 mb-1">{{ record.studyTime }}</span>
                <span class="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-0.5 rounded-full">{{ record.duration }}分钟</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 我的证书 -->
        <div v-if="activeTab === 'certificates'" class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white">我的证书</h2>
            <a href="/my-certificates" class="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium">查看全部</a>
          </div>

          <div v-if="loading" class="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div class="h-40 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
            <div class="h-40 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          </div>

          <div v-else-if="certificates.length === 0" class="text-center py-12 text-gray-500 dark:text-gray-400">
            <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p>您还没有获得任何证书</p>
            <button class="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm font-medium" @click="gotoCourses">
              浏览证书课程
            </button>
          </div>

          <div v-else class="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div v-for="cert in certificates" :key="cert.id" class="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-700 dark:to-gray-800 rounded-xl shadow overflow-hidden hover:shadow-lg transition-shadow p-1 cursor-pointer" @click="viewCertificate(cert.id)">
              <div class="bg-white dark:bg-gray-900 rounded-lg p-4 h-full flex flex-col">
                <div class="flex items-center mb-4">
                  <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <h3 class="font-bold text-gray-800 dark:text-white">{{ cert.title }}</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-400 text-sm flex-1">{{ cert.description }}</p>
                <div class="mt-4 flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
                  <span>颁发日期: {{ cert.issueDate }}</span>
                  <span class="text-blue-600 dark:text-blue-400 hover:underline">查看详情</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 账户设置 -->
        <div v-if="activeTab === 'settings'" class="p-6">
          <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-6">账户设置</h2>

          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">个人信息</label>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">姓名</label>
                  <input type="text" v-model="userInfo.name" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                </div>
                <div>
                  <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">职位</label>
                  <input type="text" v-model="userInfo.title" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                </div>
                <div>
                  <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">邮箱</label>
                  <input type="email" v-model="userInfo.email" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                </div>
                <div>
                  <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">电话</label>
                  <input type="tel" v-model="userInfo.phone" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                </div>
                <div class="md:col-span-2">
                  <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">部门</label>
                  <input type="text" v-model="userInfo.department" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                </div>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">密码设置</label>
              <div class="space-y-4">
                <div>
                  <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">当前密码</label>
                  <input type="password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">新密码</label>
                    <input type="password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                  </div>
                  <div>
                    <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">确认新密码</label>
                    <input type="password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                  </div>
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-sm font-medium">
                取消
              </button>
              <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm font-medium">
                保存设置
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 底部导航 -->
    <footer class="fixed bottom-0 left-0 w-full bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-2 z-40">
      <div class="container mx-auto">
        <div class="flex justify-around">
          <a href="/home" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              <img src="@/assets/home-icon.svg" alt="首页" class="w-6 h-6">
              <span class="text-xs mt-1">首页</span>
            </a>
          <a href="/courses" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              <img src="@/assets/courses-icon.svg" alt="课程" class="w-6 h-6">
              <span class="text-xs mt-1">课程</span>
            </a>
          <a href="/study-center" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              <img src="@/assets/study-icon.svg" alt="学习" class="w-6 h-6">
              <span class="text-xs mt-1">学习</span>
            </a>
          <a href="/profile" class="flex flex-col items-center text-blue-600 dark:text-blue-400">
              <img src="@/assets/profile-icon.svg" alt="我的" class="w-6 h-6">
              <span class="text-xs mt-1">我的</span>
            </a>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import { getUserInfo, getLearningRecords, getCertificates, getStudyStatistics } from '@/api/study';

export default {
  name: 'Profile',
  data() {
    return {
      userInfo: {},
      learningRecords: [],
      certificates: [],
      stats: {},
      activeTab: 'learning',
      loading: false
    };
  },
  computed: {
    ...mapGetters(['currentUser', 'isDarkMode']),
    darkModePath() {
      return this.isDarkMode
        ? 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z'
        : 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z';
    }
  },
  methods: {
    ...mapActions(['toggleDarkMode', 'logout']),

    // 获取用户信息
    fetchUserInfo() {
      this.loading = true;
      getUserInfo()
        .then((data) => {
          this.userInfo = data || {};
          this.loading = false;
        })
        .catch((error) => {
          console.error('Get user info failed:', error);
          this.$message.error('获取用户信息失败');
          this.loading = false;
        });
    },

    // 获取学习记录
    fetchLearningRecords() {
      getLearningRecords({
        page: 1,
        pageSize: 3
      })
        .then((data) => {
          this.learningRecords = data.list || [];
        })
        .catch((error) => {
          console.error('Get learning records failed:', error);
          this.$message.error('获取学习记录失败');
        });
    },

    // 获取证书
    fetchCertificates() {
      getCertificates({
        page: 1,
        pageSize: 2
      })
        .then((data) => {
          this.certificates = data.list || [];
        })
        .catch((error) => {
          console.error('Get certificates failed:', error);
          this.$message.error('获取证书失败');
        });
    },

    // 获取学习统计
    fetchStudyStatistics() {
      getStudyStatistics()
        .then((data) => {
          this.stats = data || {};
        })
        .catch((error) => {
          console.error('Get study statistics failed:', error);
          this.$message.error('获取学习统计失败');
        });
    },

    // 前往课程
    gotoCourse(courseId) {
      this.$router.push(`/courses/${courseId}`);
    },

    // 查看证书
    viewCertificate(certificateId) {
      this.$router.push(`/certificates/${certificateId}`);
    },

    // 前往课程列表
    gotoCourses() {
      this.$router.push('/courses');
    },

    // 保存设置
    saveSettings() {
      // 这里实现保存设置的逻辑
      this.$message.success('设置保存成功');
    }
  },
  watch: {
    activeTab: {
      handler(newTab) {
        if (newTab === 'learning') {
          this.fetchLearningRecords();
        } else if (newTab === 'certificates') {
          this.fetchCertificates();
        }
      }
    }
  },
  // 删除重复的computed属性定义
  
  mounted() {
    // 初始化深色模式
    this.$store.dispatch('initDarkMode');
    // 获取用户信息
    this.fetchUserInfo();
    // 获取学习记录
    this.fetchLearningRecords();
    // 获取学习统计
    this.fetchStudyStatistics();
  }
};
</script>

<style scoped>
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 隐藏选项卡滚动条但保留功能 */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>