<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 顶部导航 -->
    <header class="fixed top-0 left-0 w-full bg-white dark:bg-gray-800 z-50 shadow-sm">
      <div class="container mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <img src="@/assets/new-logo.svg" alt="系统logo" class="w-6 h-6 text-blue-600 dark:text-blue-400">
          <span class="text-lg font-bold text-gray-800 dark:text-white">医疗培训系统</span>
        </div>
        <div class="flex items-center space-x-4">
          <div class="relative" v-if="currentUser">
            <img
              :src="'/teacher-1.jpg'"
              alt="用户头像"
              class="w-8 h-8 rounded-full border-2 border-blue-500 cursor-pointer"
              @click="toggleUserMenu"
            >
            <div v-if="showUserMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-2 z-10">
              <a href="/profile" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">个人资料</a>
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">设置</a>
              <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
              <a href="#" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700" @click="logout">退出登录</a>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 pt-16 pb-20">
      <!-- 页面标题 -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">学习中心</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">继续您的学习，查看学习进度</p>
      </div>

      <!-- 学习进度卡片 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6 mb-8">
        <div class="flex flex-col md:flex-row md:items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-800 dark:text-white">学习进度</h2>
            <p class="text-gray-600 dark:text-gray-400 text-sm">已完成 {{ completedCoursesCount }}/{{ totalEnrolledCoursesCount }} 门课程</p>
          </div>
          <div class="mt-4 md:mt-0">
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium">
              继续学习
            </button>
          </div>
        </div>

        <div class="relative pt-1 mb-6">
          <div class="overflow-hidden h-2 mb-4 text-xs flex rounded-full bg-gray-200 dark:bg-gray-700">
            <div :style="{ width: progressPercentage + '%' }" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500 transition-all duration-500"></div>
          </div>
          <div class="flex justify-between items-center text-sm text-gray-600 dark:text-gray-400">
            <span>当前进度: {{ progressPercentage }}%</span>
            <span>目标: 完成所有已报名课程</span>
          </div>
        </div>

        <!-- 最近学习的课程 -->
        <div class="mb-6">
          <h3 class="font-medium text-gray-800 dark:text-white mb-3">最近学习的课程</h3>
          <div v-if="loading" class="space-y-4">
            <div class="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
            <div class="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          </div>
          <div v-else-if="recentCourses.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            暂无学习记录
          </div>
          <div v-else class="space-y-4">
            <div v-for="course in recentCourses" :key="course.id" class="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer" @click="resumeStudy(course.id)">
              <img :src="course.coverImage || '/default-course.jpg'" alt="课程封面" class="w-16 h-16 object-cover rounded-lg mr-4">
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-gray-800 dark:text-white truncate">{{ course.title }}</h4>
                <p class="text-gray-500 dark:text-gray-400 text-sm truncate">{{ course.currentLessonTitle }}</p>
              </div>
              <div class="ml-4 flex flex-col items-end">
                <span class="text-xs text-gray-500 dark:text-gray-400 mb-1">{{ course.lastStudyTime }}</span>
                <div class="w-24 h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div :style="{ width: course.progress + '%' }" class="h-full bg-blue-500 rounded-full"></div>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ course.progress }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 我的课程 -->
      <div class="mb-8">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold text-gray-800 dark:text-white">我的课程</h2>
          <a href="/my-courses" class="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium">查看全部</a>
        </div>

        <div v-if="loading" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="i in 6" :key="i" class="bg-white dark:bg-gray-800 rounded-xl shadow overflow-hidden animate-pulse">
            <div class="h-40 bg-gray-200 dark:bg-gray-700"></div>
            <div class="p-4 space-y-3">
              <div class="w-2/3 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="w-full h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full mt-2"></div>
            </div>
          </div>
        </div>

        <div v-else-if="myCourses.length === 0" class="text-center py-12 bg-white dark:bg-gray-800 rounded-xl shadow">
          <svg class="w-16 h-16 text-gray-400 dark:text-gray-500 mb-4 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-1">您还没有报名任何课程</h3>
          <p class="text-gray-500 dark:text-gray-400 max-w-md mx-auto mb-4">浏览课程目录，找到适合您的课程开始学习吧</p>
          <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium" @click="gotoCourses">
            浏览课程
          </button>
        </div>

        <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="course in myCourses" :key="course.id" class="bg-white dark:bg-gray-800 rounded-xl shadow overflow-hidden hover:shadow-lg transition-shadow group">
            <div class="relative h-40">
              <img :src="course.coverImage || '/default-course.jpg'" alt="课程封面" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
              <div class="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">{{ course.progress }}%</div>
            </div>
            <div class="p-4">
              <div class="flex items-center mb-2">
                <span class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">{{ getCategoryName(course.categoryId) }}</span>
                <span class="text-xs text-gray-500 dark:text-gray-400">{{ course.lessonsCount }}课时</span>
              </div>
              <h3 class="font-bold text-lg mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors cursor-pointer" @click="viewCourseDetail(course.id)">{{ course.title }}</h3>
              <p class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">{{ course.description }}</p>
              <div class="w-full h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full mb-3">
                <div :style="{ width: course.progress + '%' }" class="h-full bg-blue-500 rounded-full"></div>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ course.progress }}% 完成</span>
                <button @click="resumeStudy(course.id)" class="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors">
                  继续学习
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 学习统计 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6 mb-8">
        <div class="flex flex-col md:flex-row md:items-center justify-between mb-6">
          <h3 class="font-medium text-gray-800 dark:text-white text-lg mb-3 md:mb-0">学习时长统计</h3>
          <div class="flex space-x-2">
            <button :class="{ 'bg-blue-600 text-white': timeRange === 'week', 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300': timeRange !== 'week' }" class="px-3 py-1 rounded-md text-sm font-medium" @click="timeRange = 'week'; initStudyChart()">周</button>
            <button :class="{ 'bg-blue-600 text-white': timeRange === 'month', 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300': timeRange !== 'month' }" class="px-3 py-1 rounded-md text-sm font-medium" @click="timeRange = 'month'; initStudyChart()">月</button>
            <button :class="{ 'bg-blue-600 text-white': timeRange === 'year', 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300': timeRange !== 'year' }" class="px-3 py-1 rounded-md text-sm font-medium" @click="timeRange = 'year'; initStudyChart()">年</button>
          </div>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
          <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg">
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-1">总学习时长</p>
            <p class="text-xl font-bold text-gray-800 dark:text-white">{{ totalStudyTime }}分钟</p>
          </div>
          <div class="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg">
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-1">平均每日学习</p>
            <p class="text-xl font-bold text-gray-800 dark:text-white">{{ avgDailyStudyTime }}分钟</p>
          </div>
          <div class="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg">
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-1">学习天数</p>
            <p class="text-xl font-bold text-gray-800 dark:text-white">{{ studyDays }}天</p>
          </div>
        </div>
        
        <div class="h-64">
          <!-- 这里使用Chart.js渲染图表 -->
          <div class="w-full h-full chart-container"></div>
        </div>
      </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6">
          <h3 class="font-medium text-gray-800 dark:text-white mb-4">学习成就</h3>
          <div v-if="loading" class="space-y-4">
            <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
            <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
            <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          </div>
          <div v-else-if="achievements.length === 0" class="text-center py-6 text-gray-500 dark:text-gray-400">
            暂无学习成就
          </div>
          <div v-else class="space-y-3">
            <div v-for="achievement in achievements" :key="achievement.id" class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="w-10 h-10 rounded-full flex items-center justify-center mr-3" :class="{'bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-400': achievement.type === 'gold',
                                                                                 'bg-gray-100 text-gray-600 dark:bg-gray-600 dark:text-gray-300': achievement.type === 'silver',
                                                                                 'bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-400': achievement.type === 'bronze'}">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L12 0l4.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"></path>
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="font-medium text-gray-800 dark:text-white">{{ achievement.title }}</h4>
                <p class="text-gray-500 dark:text-gray-400 text-sm">{{ achievement.description }}</p>
              </div>
              <span class="text-xs text-gray-500 dark:text-gray-400">{{ achievement.date }}</span>
            </div>
          </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <footer class="fixed bottom-0 left-0 w-full bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-2 z-40">
      <div class="container mx-auto">
        <div class="flex justify-around">
          <a href="/home" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              <img src="@/assets/home-icon.svg" alt="首页" class="w-6 h-6">
              <span class="text-xs mt-1">首页</span>
            </a>
          <a href="/courses" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              <img src="@/assets/courses-icon.svg" alt="课程" class="w-6 h-6">
              <span class="text-xs mt-1">课程</span>
            </a>
          <a href="/study-center" class="flex flex-col items-center text-blue-600 dark:text-blue-400">
              <img src="@/assets/study-icon.svg" alt="学习" class="w-6 h-6">
              <span class="text-xs mt-1">学习</span>
            </a>
          <a href="/profile" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              <img src="@/assets/profile-icon.svg" alt="我的" class="w-6 h-6">
              <span class="text-xs mt-1">我的</span>
            </a>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
// 模拟API数据，实际项目中应替换为真实API调用
import { mockMyCourses, mockRecentStudyRecords, mockAchievements } from '@/mock/study';
import { mockCourseCategories } from '@/mock/course';
// 导入Chart.js及其组件
import { Chart, LinearScale, BarElement, BarController, Title, Tooltip, Legend } from 'chart.js';

// 注册所需组件
Chart.register(LinearScale, BarElement, BarController, Title, Tooltip, Legend);

export default {
  name: 'StudyCenter',
  data() {
    return {
      showUserMenu: false,
      myCourses: [],
      recentCourses: [],
      categories: [],
      achievements: [],
      progressPercentage: 0,
      completedCoursesCount: 0,
      totalEnrolledCoursesCount: 0,
      loading: false,
      chartInstance: null,
      timeRange: 'week', // 默认周视图
      totalStudyTime: 0,
      avgDailyStudyTime: 0,
      studyDays: 0
    };
  },
  computed: {
    ...mapGetters(['isDarkMode']),
    currentUser() {
      return this.$store.getters['user/currentUser'];
    },
    darkModePath() {
      return this.isDarkMode
        ? 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z'
        : 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z';
    }
  },
  methods: {
    ...mapActions(['toggleDarkMode', 'logout']),

    // 切换用户菜单
    toggleUserMenu() {
      this.showUserMenu = !this.showUserMenu;
    },

    // 退出登录
    handleLogout() {
      this.logout()
        .then(() => {
          this.$message.success('退出登录成功');
          this.$router.push('/login');
        })
        .catch((error) => {
          this.$message.error(error.message || '退出登录失败');
        });
    },

    // 获取课程分类
    getCourseCategories() {
      // 使用模拟数据
      this.categories = mockCourseCategories() || [];
    },

    // 获取我的课程
    fetchMyCourses() {
      this.loading = true;
      // 使用模拟数据
      const data = mockMyCourses();
      this.myCourses = data.list || [];
      this.totalEnrolledCoursesCount = data.total || 0;
      this.completedCoursesCount = this.myCourses.filter((course) => course.progress === 100).length;
      // 计算总体进度
      if (this.myCourses.length > 0) {
        const totalProgress = this.myCourses.reduce((sum, course) => sum + course.progress, 0);
        this.progressPercentage = Math.round(totalProgress / this.myCourses.length);
      } else {
        this.progressPercentage = 0;
      }
      this.loading = false;
    },

    // 获取最近学习记录
    fetchRecentStudyRecords() {
      // 使用模拟数据
      this.recentCourses = mockRecentStudyRecords({ limit: 2 }) || [];
    },

    // 获取学习成就
    fetchAchievements() {
      // 使用模拟数据
      this.achievements = mockAchievements() || [];
    },

    // 初始化学习时长图表
    initStudyChart() {
      // 根据时间范围生成不同的模拟数据
      let data;
      switch (this.timeRange) {
        case 'week':
          data = this.generateWeeklyData();
          break;
        case 'month':
          data = this.generateMonthlyData();
          break;
        case 'year':
          data = this.generateYearlyData();
          break;
        default:
          data = this.generateWeeklyData();
      }

      // 计算学习时长指标
      this.calculateStudyMetrics(data.values);

      const ctx = document.createElement('canvas');
      const chartContainer = document.querySelector('.chart-container');
      if (chartContainer) {
        chartContainer.innerHTML = '';
        chartContainer.appendChild(ctx);

        // 销毁旧图表实例（如果存在）
        if (this.chartInstance) {
          this.chartInstance.destroy();
        }

        this.chartInstance = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: data.labels || [],
            datasets: [{
              label: '学习时长（分钟）',
              data: data.values || [],
              backgroundColor: 'rgba(59, 130, 246, 0.5)',
              borderColor: 'rgb(59, 130, 246)',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                type: 'linear',
                beginAtZero: true
              }
            },
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    return `学习时长: ${context.raw}分钟`;
                  }
                }
              }
            }
          }
        });
      }
    },

    // 生成周数据
    generateWeeklyData() {
      return {
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        values: [45, 60, 30, 90, 50, 75, 65]
      };
    },

    // 生成月数据（简化为4周）
    generateMonthlyData() {
      return {
        labels: ['第1周', '第2周', '第3周', '第4周'],
        values: [280, 320, 240, 380]
      };
    },

    // 生成年数据（简化为12个月）
    generateYearlyData() {
      return {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        values: [980, 1050, 1200, 850, 1100, 1300, 1450, 1250, 1180, 1320, 1500, 1400]
      };
    },

    // 计算学习时长指标
    calculateStudyMetrics(values) {
      // 总学习时长
      this.totalStudyTime = values.reduce((sum, value) => sum + value, 0);

      // 学习天数（非零值的数量）
      this.studyDays = values.filter(value => value > 0).length;

      // 平均每日学习时长
      this.avgDailyStudyTime = this.studyDays > 0 ? Math.round(this.totalStudyTime / this.studyDays) : 0;
    },

    // 添加缺失的handleClickOutside方法
    handleClickOutside(e) {
      if (!this.$el.contains(e.target) && !e.target.closest('.relative')) {
        this.showUserMenu = false;
      }
    },

    // 修复退出登录方法名不匹配问题
    logout() {
      this.$store.dispatch('user/logout')
        .then(() => {
          this.$message.success('退出登录成功');
          this.$router.push('/login');
        })
        .catch((error) => {
          this.$message.error(error.message || '退出登录失败');
        });
    },

    // 获取分类名称
    getCategoryName(id) {
      const category = this.categories.find((item) => item.id === id);
      return category ? category.name : '未知分类';
    },

    // 继续学习
    resumeStudy(courseId) {
      this.$router.push(`/study-center/course/${courseId}`);
    },

    // 查看课程详情
    viewCourseDetail(courseId) {
      this.$router.push(`/courses/${courseId}`);
    },

    // 前往课程列表
    gotoCourses() {
      this.$router.push('/courses');
    }
  },
  // 删除重复的computed属性定义
  
  mounted() {
    // 初始化深色模式
    this.$store.dispatch('initDarkMode');
    // 获取课程分类
    this.getCourseCategories();
    // 获取我的课程
    this.fetchMyCourses();
    // 获取最近学习记录
    this.fetchRecentStudyRecords();
    // 获取学习成就
    this.fetchAchievements();
    // 初始化学习时长图表
    // 延迟执行，确保DOM已渲染
    setTimeout(() => {
      this.initStudyChart();
    }, 500);

    // 点击页面其他地方关闭用户菜单
    document.addEventListener('click', (e) => {
      if (!this.$el.contains(e.target) && !e.target.closest('.relative')) {
        this.showUserMenu = false;
      }
    });
  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener('click', this.handleClickOutside);
    // 销毁图表实例
    if (this.chartInstance) {
      this.chartInstance.destroy();
    }
  }
};
</script>

<style scoped>
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>