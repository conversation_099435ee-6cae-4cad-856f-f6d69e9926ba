import { axiosInstance } from '@/boot/main/axios';

// 获取学习中心数据
export function getStudyCenterData(data) {
  return axiosInstance({
    url: '/api/study/center',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      userId: data.userId
    }
  });
}

// 获取最近学习记录
export function getRecentLearning(data) {
  return axiosInstance({
    url: '/api/study/recent',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      userId: data.userId,
      page: data.page || 1,
      pageSize: data.pageSize || 10
    }
  });
}

// 获取我的课程列表
export function getMyCourses(data) {
  return axiosInstance({
    url: '/api/study/my-courses',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      userId: data.userId,
      status: data.status || 'all', // 'all', 'learning', 'completed'
      page: data.page || 1,
      pageSize: data.pageSize || 10
    }
  });
}

// 获取课时详情
export function getLessonDetail(data) {
  return axiosInstance({
    url: '/api/study/lesson-detail',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      lessonId: data.lessonId
    }
  });
}

export function getStudyRecordDetail(data) {
  return axiosInstance({
    url: '/api/study/record/detail',
    method: 'get',
    params: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      lessonId: data.lessonId
    }
  });
}

// 获取课程详情
export function getCourseDetail(data) {
  return axiosInstance({
    url: '/api/study/course-detail',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      courseId: data.courseId
    }
  });
}

// 获取笔记
export function getNote(data) {
  return axiosInstance({
    url: '/api/study/get-note',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      lessonId: data.lessonId
    }
  });
}

// 获取课程资源
export function getCourseResources(data) {
  return axiosInstance({
    url: '/api/course/resources',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      courseId: data.courseId,
    }
  });
}

// 获取学习成就
export function getAchievements() {
  return axiosInstance({
    url: '/api/study/achievements',
    method: 'get',
    params: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system'
    }
  });
}

// 获取学习统计数据
export function getStudyStatistics() {
  return axiosInstance({
    url: '/api/study/statistics',
    method: 'get',
    params: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system'
    }
  });
}

// 获取学习记录
export function getLearningRecords(data) {
  return axiosInstance({
    url: '/api/study/records',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      userId: data.userId,
      courseId: data.courseId,
      page: data.page || 1,
      pageSize: data.pageSize || 10
    }
  });
}

export function getCertificates(data) {
  return axiosInstance({
    url: '/api/study/certificates',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      userId: data.userId,
      courseId: data.courseId,
      page: data.page || 1,
      pageSize: data.pageSize || 10
    }
  });
}

// 获取学习进度
export function getStudyProgress(data) {
  return axiosInstance({
    url: '/api/study/progress',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      userId: data.userId,
      courseId: data.courseId
    }
  });
}

export function getUserInfo(data) {
  return axiosInstance({
    url: '/api/study/user',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      userId: data.userId,
      courseId: data.courseId
    }
  });
}


// 同步学习进度
export function syncStudyProgress(data) {
  return axiosInstance({
    url: '/api/study/sync-progress',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      userId: data.userId,
      courseId: data.courseId,
      lessonId: data.lessonId,
      progress: data.progress,
      duration: data.duration
    }
  });
}

// 保存笔记
export function saveNote(data) {
  return axiosInstance({
    url: '/api/study/note',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      userId: data.userId,
      lessonId: data.lessonId,
      content: data.content
    }
  });
}


// 提交学习笔记
export function submitStudyNote(data) {
  return axiosInstance({
    url: '/api/study/note',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      courseId: data.courseId,
      resourceId: data.resourceId,
      content: data.content
    }
  });
}