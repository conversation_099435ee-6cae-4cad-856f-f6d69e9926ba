{"name": "vue-frontend", "version": "1.0.0", "description": "Online Medical Training System MVP", "main": "index.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@fortawesome/vue-fontawesome": "^2.0.6", "autoprefixer": "^9.8.8", "axios": "^0.27.2", "chart.js": "^3.9.1", "core-js": "^3.45.0", "element-ui": "^2.15.14", "mockjs": "^1.1.0", "postcss": "^7.0.39", "swiper": "^6.8.4", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vue": "^2.6.14", "vue-awesome-swiper": "^4.1.1", "vue-router": "^3.5.3", "vuex": "^3.6.2"}, "devDependencies": {"@babel/plugin-transform-optional-chaining": "^7.27.1", "@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-service": "^4.5.19", "babel-eslint": "^10.1.0", "eslint": "^6.8.0", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}