import Vue from 'vue';
import Vuex from 'vuex';
import user from './modules/user';
import course from './modules/course';
import study from './modules/study';

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    user,
    course,
    study
  },
  // 全局state
  state: {
    loading: false,
    darkMode: false
  },
  // 全局getters
  getters: {
    isLoading: (state) => state.loading,
    isDarkMode: (state) => state.darkMode
  },
  // 全局mutations
  mutations: {
    SET_LOADING(state, payload) {
      state.loading = payload;
    },
    TOGGLE_DARK_MODE(state) {
      state.darkMode = !state.darkMode;
      // 保存到localStorage
      localStorage.setItem('darkMode', state.darkMode);
    },
    INIT_DARK_MODE(state) {
      const darkMode = localStorage.getItem('darkMode');
      if (darkMode !== null) {
        state.darkMode = JSON.parse(darkMode);
      }
    }
  },
  // 全局actions
  actions: {
    setLoading({
      commit
    }, payload) {
      commit('SET_LOADING', payload);
    },
    toggleDarkMode({
      commit
    }) {
      commit('TOGGLE_DARK_MODE');
    },
    initDarkMode({
      commit
    }) {
      commit('INIT_DARK_MODE');
    }
  }
});

export default store;