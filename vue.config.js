// 导入path模块
const path = require('path');

module.exports = {
  publicPath: '/',
  assetsDir: 'static',
  productionSourceMap: false,
  devServer: {
    port: 8080,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        target: 'http://192.168.0.173:8033',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        },
        onProxyReq: (proxyReq, req) => {
          console.log('Proxying request:', req.url, '->', 'http://192.168.0.173:8033');
        },
        onProxyRes: (proxyRes, req) => {
          console.log('Proxy response from:', req.url);
        }
      }
    }


  },
  configureWebpack: {
    resolve: {
      extensions: ['.js', '.vue', '.json'],
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    }
  }
};