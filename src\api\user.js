import { axiosInstance } from '@/boot/main/axios';

// 登录
export function login(data) {
  return axiosInstance({
    url: '/api/user/login',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      username: data.username,
      password: data.password,
      verificationCode: data.verificationCode,
      loginType: data.loginType || 'password' // 'password' 或 'verification'
    }
  });
}

// 注销
export function logout() {
  return axiosInstance({
    url: '/api/user/logout',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system'
    }
  });
}

// 获取用户信息
export function getUserInfo() {
  return axiosInstance({
    url: '/api/user/info',
    method: 'get',
    params: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system'
    }
  });
}

// 获取验证码
export function getVerificationCode(phone) {
  return axiosInstance({
    url: '/api/otms/sms/callexternalsmsservice',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      phone: phone
    }
  });
}

// 注册
export function register(data) {
  return axiosInstance({
    url: 'api/otms/basefuser/register',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      username: data.username,
      password: data.password,
      phone: data.phone,
      verificationCode: data.verificationCode,
      department: data.department,
      position: data.position
    }
  });
}