<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 顶部导航 -->
    <header class="fixed top-0 left-0 w-full bg-white dark:bg-gray-800 z-50 shadow-sm">
      <div class="container mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <img src="@/assets/new-logo.svg" class="w-6 h-6 text-blue-600 dark:text-blue-400" alt="系统logo">
          <span class="text-lg font-bold text-gray-800 dark:text-white">医疗培训系统</span>
        </div>
        <div class="flex items-center space-x-4">
          <div class="relative" v-if="currentUser">
            <img
              :src="currentUser.avatar || '/default-avatar.png'"
              alt="用户头像"
              class="w-8 h-8 rounded-full border-2 border-blue-500 cursor-pointer"
              @click="toggleUserMenu"
            >
            <div v-if="showUserMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-2 z-10">
              <a href="/profile" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">个人资料</a>
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">设置</a>
              <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
              <a href="#" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700" @click="logout">退出登录</a>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 pt-16 pb-20">
      <!-- 页面标题和筛选 -->
      <div class="mb-6 flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">课程列表</h1>
        <div class="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
          <div class="relative flex-1 sm:flex-none w-full sm:w-48">
            <select v-model="selectedCategory" class="block w-full pl-3 pr-10 py-2.5 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
              <option value="all">全部分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">{{ category.name }}</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
          <div class="relative flex-1 sm:flex-none w-full sm:w-48">
            <select v-model="sortBy" class="block w-full pl-3 pr-10 py-2.5 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
              <option value="newest">最新上线</option>
              <option value="popular">最受欢迎</option>
              <option value="price_asc">价格从低到高</option>
              <option value="price_desc">价格从高到低</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
          <div class="relative flex-1 w-full">
            <input
              type="text"
              v-model="searchKeyword"
              placeholder="搜索课程..."
              class="block w-full pl-10 pr-3 py-2.5 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 rounded-lg"
            >
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500 dark:text-gray-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- 课程列表 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <template v-if="loading">
          <div v-for="i in 6" :key="i" class="bg-white dark:bg-gray-800 rounded-xl shadow overflow-hidden animate-pulse">
            <div class="h-40 bg-gray-200 dark:bg-gray-700"></div>
            <div class="p-4 space-y-3">
              <div class="w-2/3 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="flex justify-between items-center pt-2">
                <div class="flex items-center">
                  <div class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 mr-2"></div>
                  <div class="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
                <div class="w-12 h-4 bg-blue-100 dark:bg-blue-900 rounded"></div>
              </div>
            </div>
          </div>
        </template>

        <template v-else-if="courses.length === 0">
          <div class="col-span-full flex flex-col items-center justify-center py-12 text-center">
            <svg class="w-16 h-16 text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-1">没有找到相关课程</h3>
            <p class="text-gray-500 dark:text-gray-400 max-w-md">尝试调整筛选条件或搜索关键词，看看是否能找到您感兴趣的课程。</p>
          </div>
        </template>

        <template v-else>
          <div v-for="course in courses" :key="course.id" class="bg-white dark:bg-gray-800 rounded-xl shadow overflow-hidden hover:shadow-lg transition-shadow group">
            <div class="relative h-40">
              <img :src="course.coverImage || '/default-course.jpg'" alt="课程封面" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
              <div v-if="course.isHot" class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">热门</div>
              <div v-if="course.isNew" class="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">新课</div>
            </div>
            <div class="p-4">
              <div class="flex items-center mb-2">
                <span class="text-xs" :class="{'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200': course.categoryId === 1,
                                           'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200': course.categoryId === 2,
                                           'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200': course.categoryId === 3,
                                           'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200': course.categoryId === 4,
                                           'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200': course.categoryId === 5}" px-2 py-1 rounded mr-2>{{ getCategoryName(course.categoryId) }}</span>
                <span class="text-xs text-gray-500 dark:text-gray-400">{{ course.lessonsCount }}课时</span>
              </div>
              <h3 class="font-bold text-lg mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors cursor-pointer" @click="gotoCourseDetail(course.id)">{{ course.title }}</h3>
              <p class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">{{ course.description }}</p>
              <div class="flex justify-between items-center">
                <div class="flex items-center">
                  <img :src="course.teacherAvatar || '/default-teacher.jpg'" alt="讲师头像" class="w-8 h-8 rounded-full mr-2">
                  <span class="text-sm text-gray-700 dark:text-gray-300">{{ course.teacherName }}</span>
                </div>
                <div class="text-blue-600 dark:text-blue-400 font-bold">{{ course.price > 0 ? '¥' + course.price : '免费' }}</div>
              </div>
            </div>
          </div>
        </template>
      </div>

      <!-- 分页 -->
      <div v-if="!loading && courses.length > 0" class="mt-8 flex justify-center">
        <nav class="inline-flex rounded-md shadow" aria-label="Pagination">
          <button @click="prevPage" :disabled="currentPage === 1" class="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-l-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            上一页
          </button>
          <button v-for="page in pageCount" :key="page" @click="goToPage(page)" :class="{'px-4 py-2 text-sm font-medium bg-blue-500 text-white border border-blue-500': currentPage === page,
                                                                                      'px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700': currentPage !== page}" transition-colors>
            {{ page }}
          </button>
          <button @click="nextPage" :disabled="currentPage === pageCount" class="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-r-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            下一页
          </button>
        </nav>
      </div>
    </main>

    <!-- 底部导航 -->
    <footer class="fixed bottom-0 left-0 w-full bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-2 z-40">
      <div class="container mx-auto">
        <div class="flex justify-around">
          <a href="/home" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              <img src="@/assets/home-icon.svg" alt="首页" class="w-6 h-6">
              <span class="text-xs mt-1">首页</span>
            </a>
          <a href="/courses" class="flex flex-col items-center text-blue-600 dark:text-blue-400">
              <img src="@/assets/courses-icon.svg" alt="课程" class="w-6 h-6">
              <span class="text-xs mt-1">课程</span>
            </a>
          <a href="/study-center" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              <img src="@/assets/study-icon.svg" alt="学习" class="w-6 h-6">
              <span class="text-xs mt-1">学习</span>
            </a>
          <a href="/profile" class="flex flex-col items-center text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              <img src="@/assets/profile-icon.svg" alt="我的" class="w-6 h-6">
              <span class="text-xs mt-1">我的</span>
            </a>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import { getCourseList, getCourseCategories } from '@/api/course';

export default {
  name: 'Courses',
  data() {
    return {
      showUserMenu: false,
      courses: [],
      categories: [],
      selectedCategory: 'all',
      sortBy: 'newest',
      searchKeyword: '',
      currentPage: 1,
      pageSize: 9,
      totalCount: 0,
      loading: false
    };
  },
  computed: {
    ...mapGetters(['currentUser', 'isDarkMode']),
    pageCount() {
      return Math.ceil(this.totalCount / this.pageSize);
    },
    darkModePath() {
      return this.isDarkMode
        ? 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z'
        : 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z';
    }
  },
  methods: {
    ...mapActions(['toggleDarkMode', 'logout']),

    // 切换用户菜单
    toggleUserMenu() {
      this.showUserMenu = !this.showUserMenu;
    },

    // 退出登录处理
    handleLogout() {
      this.logout()
        .then(() => {
          this.$message.success('退出登录成功');
          this.$router.push('/login');
        })
        .catch((error) => {
          this.$message.error(error.message || '退出登录失败');
        });
    },

    // 获取课程分类
    getCourseCategories() {
      getCourseCategories()
        .then((data) => {
          this.categories = data || [];
        })
        .catch((error) => {
          console.error('Get course categories failed:', error);
          this.$message.error('获取课程分类失败');
        });
    },

    // 获取课程列表
    fetchCourses() {
      this.loading = true;
      const params = {
        page: this.currentPage,
        pageSize: this.pageSize,
        categoryId: this.selectedCategory !== 'all' ? this.selectedCategory : undefined,
        sortBy: this.sortBy,
        keyword: this.searchKeyword
      };

      getCourseList(params)
        .then((data) => {
          this.courses = data.list || [];
          this.totalCount = data.total || 0;
          this.loading = false;
        })
        .catch((error) => {
          console.error('Get course list failed:', error);
          this.$message.error('获取课程列表失败');
          this.loading = false;
        });
    },

    // 获取分类名称
    getCategoryName(id) {
      const category = this.categories.find((item) => item.id === id);
      return category ? category.name : '未知分类';
    },

    // 前往课程详情
    gotoCourseDetail(id) {
      this.$router.push(`/courses/${id}`);
    },

    // 上一页
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
        this.fetchCourses();
      }
    },

    // 下一页
    nextPage() {
      if (this.currentPage < this.pageCount) {
        this.currentPage++;
        this.fetchCourses();
      }
    },

    // 跳转到指定页
    goToPage(page) {
      if (page !== this.currentPage && page >= 1 && page <= this.pageCount) {
        this.currentPage = page;
        this.fetchCourses();
      }
    }
  },
  watch: {
    selectedCategory: {
      handler() {
        this.currentPage = 1;
        this.fetchCourses();
      }
    },
    sortBy: {
      handler() {
        this.currentPage = 1;
        this.fetchCourses();
      }
    },
    searchKeyword: {
      handler() {
        this.currentPage = 1;
        // 使用防抖，避免频繁请求
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
          this.fetchCourses();
        }, 500);
      }
    }
  },
    mounted() {
    // 初始化深色模式
    this.$store.dispatch('initDarkMode');
    // 获取课程分类
    this.getCourseCategories();
    // 获取课程列表
    this.fetchCourses();

    // 点击页面其他地方关闭用户菜单
    document.addEventListener('click', (e) => {
      if (!this.$el.contains(e.target) && !e.target.closest('.relative')) {
        this.showUserMenu = false;
      }
    });
  },
  beforeDestroy() {
    // 清除定时器
    clearTimeout(this.searchTimeout);
    // 移除事件监听
    // 移除事件监听（注意：这里不需要，因为我们使用的是匿名函数）
    // document.removeEventListener('click', this.handleClickOutside);
  }
};
</script>

<style scoped>
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 分页按钮样式 */
button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
</style>