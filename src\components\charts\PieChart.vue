<template>
  <div class="pie-chart-container">
    <canvas ref="pieChart"></canvas>
  </div>
</template>

<script>
import { Chart } from 'chart.js';

export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    data: {
      type: Object,
      required: true
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chartInstance: null
    };
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.destroy();
    }
  },
  methods: {
    initChart() {
      const ctx = this.$refs.pieChart;
      if (ctx) {
        this.chartInstance = new Chart(ctx, {
          type: 'pie',
          data: this.data,
          options: this.options
        });
      }
    }
  },
  watch: {
    data: {
      deep: true,
      handler() {
        if (this.chartInstance) {
          this.chartInstance.destroy();
          this.initChart();
        }
      }
    }
  }
};
</script>

<style scoped>
.pie-chart-container {
  width: 100%;
  height: 100%;
}
</style>