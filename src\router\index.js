import Vue from 'vue';
import Router from 'vue-router';

// 懒加载路由组件
const Login = () => import('@/views/login/index');
const Home = () => import('@/views/home/<USER>');
const Courses = () => import('@/views/courses/index');
const CourseDetail = () => import('@/views/courses/detail.vue');
const StudyCenter = () => import('@/views/study-center/index');
const Learning = () => import('@/views/study-center/learning.vue');
const Profile = () => import('@/views/profile/index');
const NotFound = () => import('@/views/404/index.vue');

Vue.use(Router);

const router = new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  routes: [
    {
      path: '/',
      redirect: '/home'
    },
    {
      path: '/login',
      name: 'Login',
      component: Login,
      meta: {
        requiresAuth: false,
        title: '登录'
      }
    },
    {
      path: '/home',
      name: 'Home',
      component: Home,
      meta: {
        requiresAuth: true,
        title: '首页'
      }
    },
    {
      path: '/courses',
      name: 'Courses',
      component: Courses,
      meta: {
        requiresAuth: true,
        title: '课程列表'
      }
    },
    {
      path: '/courses/:id',
      name: 'CourseDetail',
      component: CourseDetail,
      meta: {
        requiresAuth: true,
        title: '课程详情'
      }
    },
    {
      path: '/learning/:courseId/:lessonId',
      name: 'Learning',
      component: Learning,
      meta: {
        requiresAuth: true,
        title: '课程学习'
      }
    },
    {
      path: '/study-center',
      name: 'StudyCenter',
      component: StudyCenter,
      meta: {
        requiresAuth: true,
        title: '学习中心'
      }
    },
    {
      path: '/profile',
      name: 'Profile',
      component: Profile,
      meta: {
        requiresAuth: true,
        title: '个人资料'
      }
    },
    // 404页面
    {
      path: '*',
      component: NotFound,
      meta: {
        requiresAuth: false,
        title: '页面不存在'
      }
    }
  ]
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || '在线医疗培训系统';

  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    // 检查是否已登录
    const token = localStorage.getItem('token');
    if (token) {
      // 如果已登录且要去登录页，重定向到首页
      if (to.path === '/login') {
        next({ path: '/home' });
      } else {
        next();
      }
    } else {
      // 未登录且不是去登录页，重定向到登录页
      if (to.path !== '/login') {
        next({ path: '/login' });
      } else {
        next();
      }
    }
  } else {
    // 不需要登录的页面
    // 如果已登录且要去登录页，重定向到首页
    if (to.path === '/login' && localStorage.getItem('token')) {
      next({ path: '/home' });
    } else {
      next();
    }
  }
});

export default router;