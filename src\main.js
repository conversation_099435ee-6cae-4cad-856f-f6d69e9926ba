import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import './styles/index.css';
import { axiosInstance } from './boot/main/axios';

// 导入Font Awesome
import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { faUser, faLock, faEye, faEyeSlash, faMoon, faSun, faGraduationCap, faShieldAlt, faPhone } from '@fortawesome/free-solid-svg-icons';

// 添加图标到库
library.add(faUser, faLock, faEye, faEyeSlash, faMoon, faSun, faGraduationCap, faShieldAlt, faPhone);

// 注册全局组件
Vue.component('font-awesome-icon', FontAwesomeIcon);

// 开发环境下启用Mock服务
// if (process.env.NODE_ENV === 'development') {
//   const { initMock } = require('./mock');
//   initMock();
// }

Vue.use(ElementUI);

// 将axiosInstance挂载到Vue原型上
Vue.prototype.$axios = axiosInstance;

Vue.config.productionTip = false;

// 初始化深色模式
store.commit('INIT_DARK_MODE');

new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#app');

// 全局错误处理
Vue.config.errorHandler = function (err, vm, info) {
  console.error('Error:', err);
  console.error('Vue instance:', vm);
  console.error('Error info:', info);
};