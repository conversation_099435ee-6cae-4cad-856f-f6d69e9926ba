import { axiosInstance } from '@/boot/main/axios';

// 获取课程列表
export function getCourseList(params) {
  return axiosInstance({
    url: '/api/course/list',
    method: 'get',
    params: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      category: params.category || '',
      keyword: params.keyword || '',
      status: params.status || ''
    }
  });
}

// 获取课程详情
export function getCourseDetail(courseId) {
  return axiosInstance({
    url: '/api/course/detail',
    method: 'get',
    params: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      courseId: courseId
    }
  });
}

// 报名课程
export function enrollCourse(courseId) {
  return axiosInstance({
    url: '/api/course/enroll',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      courseId: courseId
    }
  });
}

// 获取课程资源
export function getCourseResources(data) {
  return axiosInstance({
    url: '/api/course/resources',
    method: 'post',
    data: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system',
      courseId: data.courseId,
      moduleId: data.moduleId || ''
    }
  });
}

// 获取课程分类
export function getCourseCategories() {
  return axiosInstance({
    url: '/api/course/categories',
    method: 'get',
    params: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system'
    }
  });
}

// 获取课程进度
export function getCourseProgress() {
  return axiosInstance({
    url: '/api/course/progress',
    method: 'get',
    params: {
      AppID: process.env.VUE_APP_ID || 'medical-training-system'
    }
  });
}