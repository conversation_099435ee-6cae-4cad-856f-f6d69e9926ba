// src/mock/study.js
// 模拟学习相关数据

// 生成随机ID
function generateId() {
  return Math.random().toString(36).substr(2, 9);
}

// 模拟我的课程数据
export function mockMyCourses() {
  return {
    list: [
      {
        id: generateId(),
        title: '人体解剖学基础',
        description: '系统介绍人体各系统的解剖结构和生理功能，为临床实践奠定基础。',
        coverImage: '/course-1.jpg',
        categoryId: 1,
        lessonsCount: 12,
        progress: 65,
        lastStudyTime: '2023-11-15 14:30'
      },
      {
        id: generateId(),
        title: '内科学临床技能',
        description: '掌握内科常见疾病的诊断、治疗和护理方法，提高临床思维能力。',
        coverImage: '/course-2.jpg',
        categoryId: 2,
        lessonsCount: 18,
        progress: 30,
        lastStudyTime: '2023-11-14 09:15'
      },
      {
        id: generateId(),
        title: '药理学基础',
        description: '介绍药物的作用机制、临床应用、不良反应及用药监护要点。',
        coverImage: '/course-3.jpg',
        categoryId: 4,
        lessonsCount: 15,
        progress: 100,
        lastStudyTime: '2023-11-10 16:45'
      },
      {
        id: generateId(),
        title: '护理伦理学',
        description: '探讨护理实践中的伦理问题和道德决策，培养职业伦理素养。',
        coverImage: '/course-4.jpg',
        categoryId: 3,
        lessonsCount: 8,
        progress: 80,
        lastStudyTime: '2023-11-08 10:20'
      }
    ],
    total: 4
  };
}

// 模拟最近学习记录
export function mockRecentStudyRecords({ limit = 2 }) {
  return [
    {
      id: generateId(),
      title: '人体解剖学基础',
      coverImage: '/course-1.jpg',
      currentLessonTitle: '第7课：呼吸系统解剖',
      lastStudyTime: '2小时前',
      progress: 65
    },
    {
      id: generateId(),
      title: '内科学临床技能',
      coverImage: '/course-2.jpg',
      currentLessonTitle: '第5课：心血管疾病诊断',
      lastStudyTime: '1天前',
      progress: 30
    }
  ].slice(0, limit);
}

// 模拟学习统计数据
export function mockStudyStatistics() {
  return {
    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    values: [45, 60, 30, 90, 50, 75, 65]
  };
}

// 模拟学习成就数据
export function mockAchievements() {
  return [
    {
      id: generateId(),
      title: '学习新手',
      description: '完成第一门课程的10%',
      type: 'bronze',
      date: '2023-10-20'
    },
    {
      id: generateId(),
      title: '知识探索者',
      description: '完成3门不同分类的课程',
      type: 'silver',
      date: '2023-11-05'
    },
    {
      id: generateId(),
      title: '学习达人',
      description: '连续7天学习',
      type: 'gold',
      date: '2023-11-10'
    }
  ];
}